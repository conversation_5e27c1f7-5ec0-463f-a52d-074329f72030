

redeem-menu:
  type: inventory
  title: <&3><&l>Reedem
  size: 45
  inventory: chest
  gui: true
  slots:
  - "[] [] [] [] [] [] [] [] []"
  - "[] [] [] [cosmetics] [] [xpbomb-redeem] [] [] []"
  - "[] [] [] [] [] [] [] [] []"
  - "[] [kingdoms] [] [keepInventory] [] [safariNetsSingle] [] [safariNetsReuse] []"
  - "[] [] [] [] [] [] [] [] []"

redeem-menu-alt:
  type: inventory
  title: <&3><&l>Reedem
  size: 27
  inventory: chest
  gui: true
  slots:
  - "[] [] [] [] [] [] [] [] []"
  - "[] [] [] [cosmetics] [] [xpbomb-redeem] [] [] []"
  - "[] [] [] [] [] [] [] [] []"

kingdoms:
  type: item
  material: shield
  display name: <&b><&l><&b>kingdoms Booster
  lore:
  - '<&6>Redeem an extra 5 power for your kingdom!'
  - ''
  - 'Available: <placeholder[specializedcrates_virtual_keys_kingdomBooster]>'
    
xpbomb-redeem:
  type: item
  material: experience_bottle
  display name: <&b><&l><&b>XP Bomb
  mechanisms:
    unbreakable: true
    hides:
    - ENCHANTS
    - ITEM_DATA
    custom_model_data: 10000
  enchantments:
  - unbreaking:1
  lore:
  - '<&6>Activate double bending uses for 20 minutes!'
  - ''
  - 'Available: <placeholder[specializedcrates_virtual_keys_xpBomb]>'
    
keepInventory:
  type: item
  material: sunflower
  display name: <&b><&l><&b>Keep Inventory Token
  lore:
  - '<&6>Redeem 5 keep inventory tokens!'
  - ''
  - 'Available: <placeholder[specializedcrates_virtual_keys_keepInv]>'
    
safariNetsSingle:
  type: item
  material: wolf_spawn_egg
  display name: <&b><&l><&b>Safari Net (Single Use)
  lore:
  - '<&6>Redeem safari nets to capture mobs!'
  - ''
  - 'Available: <placeholder[specializedcrates_virtual_keys_netsSingle]>'
    
safariNetsReuse:
  type: item
  material: wolf_spawn_egg
  display name: <&b><&l><&b>Safari Net (Reusable)
  lore:
  - '<&6>Redeem safari nets to capture mobs!'
  - ''
  - 'Available: <placeholder[specializedcrates_virtual_keys_netsReuse]>'
   
cosmetics:
  type: item
  material: feather
  mechanisms:
    hides:
    - ITEM_DATA
    custom_model_data: 10116
  display name: <&b><&l><&b>Cosmetics
  lore:
  - '<&6>Redeem cosmetics!'
  - '<&f><&b>You can also do <&b><&l>/claimitems <&f><&b>directly.'
  - ''
  - '<&b><&o>Click to see your claimable items'
  

redeem_command:
  type: command
  name: redeem
  description: redeem
  usage: /redeem
  allowed help:
  - determine <player.is_op>
  script:
  - if <placeholder[server_name]||null> != null:
    - inventory open d:redeem-menu-alt
  - else if <placeholder[server_name]||null> == "Remove-Dev":
    - inventory open d:redeem-menu
  - determine FULFILLED

redeem_handler:
  type: world
  events:
    on player clicks in redeem-menu:
    - if <context.action> == HOTBAR_SWAP:
      - determine cancelled
    - if <context.is_shift_click>:
      - determine cancelled
    on player drags in redeem-menu:
    - determine cancelled

    on player clicks in redeem-menu-alt:
    - if <context.action> == HOTBAR_SWAP:
      - determine cancelled
    - if <context.is_shift_click>:
      - determine cancelled
    on player drags in redeem-menu-alt:
    - determine cancelled

    on player clicks cosmetics in redeem-menu:
    - execute as_player "claimitems"

    on player clicks cosmetics in redeem-menu-alt:
    - execute as_player "claimitems"
    
    on player clicks kingdoms in redeem-menu:
    - inventory close d:redeem-menu
    - if <placeholder[specializedcrates_virtual_keys_kingdomBooster]> >= 1:
      - execute as_server "k admin maxlandmodifier <placeholder[kingdoms_name]> add 5"
      - execute as_server "crate givekey kingdomBooster <player.name> -1 -v"
    - else:
      - narrate "<&4>Oops! You don't have any of these to redeem."
    on player clicks xpbomb-redeem in redeem-menu:
    - inventory close d:redeem-menu
    - if <placeholder[specializedcrates_virtual_keys_xpBomb]> >= 1:
      - execute as_server "crate forceopen xpBomb <player.name>"
      - execute as_server "crate givekey xpBomb <player.name> -1 -v"
    - else:
      - narrate "<&4>Oops! You don't have any of these to redeem."
    on player clicks xpbomb-redeem in redeem-menu-alt:
    - inventory close d:redeem-menu-alt
    - if <placeholder[specializedcrates_virtual_keys_xpBomb]> >= 1:
      - execute as_server "crate forceopen xpBomb <player.name>"
      - execute as_server "crate givekey xpBomb <player.name> -1 -v"
    - else:
      - narrate "<&4>Oops! You don't have any of these to redeem."
    on player clicks keepInventory in redeem-menu:
    - inventory close d:redeem-menu
    - if <placeholder[specializedcrates_virtual_keys_keepInv]> >= 1:
      - execute as_server "crate forceopen keepInv <player.name>"
      - execute as_server "crate givekey keepInv <player.name> -1 -v"
    - else:
      - narrate "<&4>Oops! You don't have any of these to redeem."
    on player clicks keepInventory in redeem-menu-alt:
    - inventory close d:redeem-menu-alt
    - if <placeholder[specializedcrates_virtual_keys_keepInv]> >= 1:
      - execute as_server "crate forceopen keepInv <player.name>"
      - execute as_server "crate givekey keepInv <player.name> -1 -v"
    - else:
      - narrate "<&4>Oops! You don't have any of these to redeem."
    on player clicks safariNetsSingle in redeem-menu:
    - inventory close d:redeem-menu
    - if <placeholder[specializedcrates_virtual_keys_netsSingle]> >= 1:
      - execute as_server "crate forceopen netsSingle <player.name>"
      - execute as_server "crate givekey netsSingle <player.name> -1 -v"
    - else:
      - narrate "<&4>Oops! You don't have any of these to redeem."
    on player clicks safariNetsSingle in redeem-menu-alt:
    - inventory close d:redeem-menu-alt
    - if <placeholder[specializedcrates_virtual_keys_netsSingle]> >= 1:
      - execute as_server "crate forceopen netsSingle <player.name>"
      - execute as_server "crate givekey netsSingle <player.name> -1 -v"
    - else:
      - narrate "<&4>Oops! You don't have any of these to redeem."
    on player clicks safariNetsReuse in redeem-menu:
    - inventory close d:redeem-menu
    - if <placeholder[specializedcrates_virtual_keys_netsReuse]> >= 1:
      - execute as_server "crate forceopen netsReuse <player.name>"
      - execute as_server "crate givekey netsReuse <player.name> -1 -v"
    - else:
      - narrate "<&4>Oops! You don't have any of these to redeem."
    on player clicks safariNetsReuse in redeem-menu-alt:
    - inventory close d:redeem-menu-alt
    - if <placeholder[specializedcrates_virtual_keys_netsReuse]> >= 1:
      - execute as_server "crate forceopen netsReuse <player.name>"
      - execute as_server "crate givekey netsReuse <player.name> -1 -v"
    - else:
      - narrate "<&4>Oops! You don't have any of these to redeem."
      

