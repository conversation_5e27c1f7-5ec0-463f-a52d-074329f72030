chest_reader:
    type: task
    debug: false
    script:
    # Get the chest the player is looking at
    - define chest_location <player.cursor_on>
    
    # Check if the location has an inventory (is a chest or similar container)
    - if !<[chest_location].has_inventory>:
        - narrate "<&c>You must be looking at a chest or container!"
        - stop
    
    # Get the inventory of the chest
    - define chest_inventory <[chest_location].inventory>
    
    # Get all items from the chest
    - define chest_items <[chest_inventory].list_contents>
    
    # Check if chest is empty
    - if <[chest_items].is_empty>:
        - narrate "<&c>The chest is empty!"
        - stop
    
    # Create a map to store item data
    - definemap item_data
    
    # Process each item in the chest
    - foreach <[chest_items]> as:item:
        # Skip air/empty slots
        - if <[item].material> == air:
            - foreach next
        
        # Get item namespace (material namespace)
        - define namespace <[item].material.name.before[:]||minecraft>
        - define material_name <[item].material.name.after[:]||<[item].material.name>>
        
        # Get item data (NBT will be included in the full item string)
        - define item_string <[item]>

        # Create item entry with all relevant data
        - definemap item_entry:
            slot: <[loop_index]>
            material: <[material_name]>
            namespace: <[namespace]>
            quantity: <[item].quantity>
            display_name: <[item].display||"">
            lore: <[item].lore||<list>>
            enchantments: <[item].enchantment_map||<map>>
            custom_data: <[item].custom_data||<map>>
            full_item_string: <[item_string]>
            raw_item: <[item]>
        
        # Add to main data map
        - define item_data[<[loop_index]>] <[item_entry]>
    
    # Create metadata for the save
    - definemap save_data:
        timestamp: <util.time_now>
        chest_location: <[chest_location]>
        chest_world: <[chest_location].world.name>
        player: <player.name>
        total_items: <[chest_items].size>
        items: <[item_data]>
    
    # Generate filename with timestamp
    - define timestamp <util.time_now.format[yyyy-MM-dd_HH-mm-ss]>
    - define filename "chest_data_<[timestamp]>.yml"

    # Create the YAML data structure and save
    - yaml create id:chest_save

    # Set each piece of data individually to avoid issues
    - yaml set id:chest_save key:timestamp value:<[save_data].get[timestamp]>
    - yaml set id:chest_save key:chest_location value:<[save_data].get[chest_location]>
    - yaml set id:chest_save key:chest_world value:<[save_data].get[chest_world]>
    - yaml set id:chest_save key:player value:<[save_data].get[player]>
    - yaml set id:chest_save key:total_items value:<[save_data].get[total_items]>
    - yaml set id:chest_save key:items value:<[save_data].get[items]>

    # Save the file to the scripts directory (most reliable location)
    - yaml savefile id:chest_save "<[filename]>"
    - yaml unload id:chest_save

    # Also save a simple text version as backup
    - define text_filename "chest_data_<[timestamp]>.txt"
    - define text_content "Chest Data Export<&nl>==================<&nl>Timestamp: <[save_data].get[timestamp]><&nl>Location: <[save_data].get[chest_location]><&nl>World: <[save_data].get[chest_world]><&nl>Player: <[save_data].get[player]><&nl>Total Items: <[save_data].get[total_items]><&nl><&nl>Items:<&nl><[save_data].get[items].to_json.formatted>"
    - ~filewrite path:<[text_filename]> data:<[text_content]>

    # Notify player
    - narrate "<&a>Successfully saved <[item_data].size> items from chest!"
    - narrate "<&7>Location: <[chest_location]>"
    - narrate "<&7>YAML file: plugins/Denizen/scripts/<[filename]>"
    - narrate "<&7>Text file: plugins/Denizen/scripts/<[text_filename]>"
    - narrate "<&7>Use '/ex yaml load id:chest_data <[filename]>' to load the data"

# Command to run the script
chest_read_command:
    type: command
    name: readchest
    description: Reads items from the chest you're looking at and saves to YAML
    usage: /readchest
    permission: denizen.readchest
    script:
    - run chest_reader

# Alternative version that also includes detailed NBT analysis
chest_reader_detailed:
    type: task
    debug: false
    script:
    # Get the chest the player is looking at
    - define chest_location <player.cursor_on>
    
    # Check if the location has an inventory
    - if !<[chest_location].has_inventory>:
        - narrate "<&c>You must be looking at a chest or container!"
        - stop
    
    # Get the inventory of the chest
    - define chest_inventory <[chest_location].inventory>
    
    # Get all items from the chest
    - define chest_items <[chest_inventory].list_contents>
    
    # Check if chest is empty
    - if <[chest_items].is_empty>:
        - narrate "<&c>The chest is empty!"
        - stop
    
    # Create a map to store item data
    - definemap item_data
    
    # Process each item in the chest
    - foreach <[chest_items]> as:item:
        # Skip air/empty slots
        - if <[item].material> == air:
            - foreach next
        
        # Get comprehensive item data
        - define namespace <[item].material.name.before[:]||minecraft>
        - define material_name <[item].material.name.after[:]||<[item].material.name>>

        # Get all available item properties
        - definemap item_entry:
            slot: <[loop_index]>
            material: <[material_name]>
            namespace: <[namespace]>
            quantity: <[item].quantity>
            display_name: <[item].display||"">
            lore: <[item].lore||<list>>
            enchantments: <[item].enchantment_map||<map>>
            durability: <[item].durability||0>
            max_durability: <[item].max_durability||0>
            custom_model_data: <[item].custom_model_data||0>
            flags: <[item].flags||<list>>
            custom_data: <[item].custom_data||<map>>
            full_item_string: <[item]>
            script_name: <[item].script.name||"">
            is_script_item: <[item].has_script>

        # Add to main data map
        - define item_data[<[loop_index]>] <[item_entry]>

    # Create comprehensive metadata
    - definemap save_data:
        metadata:
            timestamp: <util.time_now>
            formatted_time: <util.time_now.format[yyyy-MM-dd HH:mm:ss]>
            chest_location: <[chest_location]>
            chest_coordinates: "x:<[chest_location].x> y:<[chest_location].y> z:<[chest_location].z>"
            chest_world: <[chest_location].world.name>
            player: <player.name>
            player_uuid: <player.uuid>
            total_items: <[chest_items].size>
            non_empty_slots: <[item_data].size>
            chest_type: <[chest_location].material.name>
        items: <[item_data]>

    # Generate filename with timestamp
    - define timestamp <util.time_now.format[yyyy-MM-dd_HH-mm-ss]>
    - define filename chest_detailed_<[timestamp]>.yml

    # Save to YAML file
    - yaml create id:chest_save_detailed
    - yaml set id:chest_save_detailed key:metadata value:<[save_data].get[metadata]>
    - yaml set id:chest_save_detailed key:items value:<[save_data].get[items]>
    - yaml savefile id:chest_save_detailed <[filename]>
    - yaml unload id:chest_save_detailed
    
    # Notify player with detailed info
    - narrate "<&a>Successfully saved detailed data for <[item_data].size> items from chest"
    - narrate "<&7>Location: <[chest_location]> in world '<[chest_location].world.name>'"
    - narrate "<&7>File saved to: plugins/Denizen/data/<[filename]>"
    - narrate "<&7>Total slots checked: <[chest_items].size>, Non-empty slots: <[item_data].size>"

# Command for detailed version
chest_read_detailed_command:
    type: command
    name: readchestdetailed
    description: Reads items from chest with detailed NBT analysis and saves to YAML
    usage: /readchestdetailed
    permission: denizen.readchest
    script:
    - run chest_reader_detailed
